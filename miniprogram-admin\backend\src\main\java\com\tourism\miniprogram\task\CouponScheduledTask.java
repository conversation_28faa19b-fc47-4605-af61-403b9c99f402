package com.tourism.miniprogram.task;

import com.tourism.miniprogram.service.CouponService;
import com.tourism.miniprogram.service.CouponUsageRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 数字卡券定时任务
 * 
 * 主要功能：
 * 1. 定时处理过期卡券，将满足条件的卡券状态更新为'used'
 * 2. 处理条件：used_at不为null（已被使用）且valid_to小于当前时间（已过期）且status不等于'used'（避免重复更新）
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@Component
public class CouponScheduledTask {

    @Autowired
    private CouponService couponService;

    @Autowired
    private CouponUsageRecordService couponUsageRecordService;

    /**
     * 处理过期卡券的定时任务
     *
     * 执行频率：每分钟执行一次
     * Cron表达式：0 * * * * ? 表示每分钟的第0秒执行
     *
     * 任务逻辑：
     * 1. 查询满足过期条件的卡券
     * 2. 批量更新这些卡券的状态为'used'
     * 3. 记录处理结果日志
     */
    @Scheduled(cron = "0 * * * * ?")
    public void processExpiredCoupons() {
        try {
            log.info("=== 开始执行过期卡券处理定时任务 ===");

            long startTime = System.currentTimeMillis();

            // 调用服务层方法处理过期卡券
            int processedCount = couponService.processExpiredCoupons();

            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            if (processedCount > 0) {
                log.info("=== 过期卡券处理定时任务完成 === 处理数量: {}, 耗时: {}ms", processedCount, duration);
            } else {
                log.debug("=== 过期卡券处理定时任务完成 === 无需处理的卡券, 耗时: {}ms", duration);
            }

        } catch (Exception e) {
            log.error("=== 过期卡券处理定时任务执行失败 ===", e);

            // 这里可以添加告警通知逻辑，比如发送邮件或短信通知管理员
            // 例如：alertService.sendAlert("过期卡券处理定时任务执行失败", e.getMessage());
        }
    }

    /**
     * 处理过期卡券使用记录的定时任务
     *
     * 执行频率：每分钟执行一次
     * Cron表达式：0 * * * * ? 表示每分钟的第0秒执行
     *
     * 任务逻辑：
     * 1. 查询满足过期条件的卡券使用记录
     * 2. 批量更新这些记录的状态为'used'
     * 3. 记录处理结果日志
     */
    @Scheduled(cron = "0 * * * * ?")
    public void processExpiredUsageRecords() {
        try {
            log.info("=== 开始执行过期卡券使用记录处理定时任务 ===");

            long startTime = System.currentTimeMillis();

            // 调用服务层方法处理过期使用记录
            int processedCount = couponUsageRecordService.processExpiredUsageRecords();

            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            if (processedCount > 0) {
                log.info("=== 过期卡券使用记录处理定时任务完成 === 处理数量: {}, 耗时: {}ms", processedCount, duration);
            } else {
                log.debug("=== 过期卡券使用记录处理定时任务完成 === 无需处理的记录, 耗时: {}ms", duration);
            }

        } catch (Exception e) {
            log.error("=== 过期卡券使用记录处理定时任务执行失败 ===", e);

            // 这里可以添加告警通知逻辑，比如发送邮件或短信通知管理员
            // 例如：alertService.sendAlert("过期卡券使用记录处理定时任务执行失败", e.getMessage());
        }
    }

    /**
     * 系统启动后延迟执行的过期数据处理任务
     *
     * 延迟时间：系统启动后30秒执行一次
     * 目的：确保系统完全启动后立即处理一次过期卡券和使用记录
     */
    @Scheduled(initialDelay = 30000, fixedDelay = Long.MAX_VALUE)
    public void processExpiredDataOnStartup() {
        try {
            log.info("=== 系统启动后执行过期数据处理任务 ===");

            // 处理过期卡券
            int processedCoupons = couponService.processExpiredCoupons();

            // 处理过期使用记录
            int processedRecords = couponUsageRecordService.processExpiredUsageRecords();

            log.info("=== 系统启动后过期数据处理完成 === 过期卡券: {}, 过期使用记录: {}",
                    processedCoupons, processedRecords);

        } catch (Exception e) {
            log.error("=== 系统启动后过期数据处理任务执行失败 ===", e);
        }
    }

    /**
     * 每小时执行的卡券状态统计任务（可选）
     * 
     * 执行频率：每小时的第0分0秒执行
     * Cron表达式：0 0 * * * ? 表示每小时执行一次
     * 
     * 目的：定期统计各种状态的卡券数量，便于监控系统运行状况
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void statisticsCouponStatus() {
        try {
            log.info("=== 开始执行卡券状态统计任务 ===");
            
            // 统计各种状态的卡券数量
            int unactivatedCount = couponService.getCouponsByStatus("unactivated").size();
            int activeCount = couponService.getCouponsByStatus("active").size();
            int usedCount = couponService.getCouponsByStatus("used").size();
            int expiredCount = couponService.getCouponsByStatus("expired").size();
            
            int totalCount = unactivatedCount + activeCount + usedCount + expiredCount;
            
            log.info("=== 卡券状态统计结果 === 总数: {}, 未激活: {}, 已激活: {}, 已使用: {}, 已过期: {}", 
                totalCount, unactivatedCount, activeCount, usedCount, expiredCount);
            
        } catch (Exception e) {
            log.error("=== 卡券状态统计任务执行失败 ===", e);
        }
    }
}
