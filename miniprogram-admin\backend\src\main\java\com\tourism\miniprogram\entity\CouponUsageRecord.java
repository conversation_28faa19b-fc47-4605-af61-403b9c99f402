package com.tourism.miniprogram.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 卡券使用记录实体类
 *
 * <AUTHOR> Team
 * @since 2025-06-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("coupon_usage_records")
@ApiModel(value = "CouponUsageRecord对象", description = "卡券使用记录信息")
public class CouponUsageRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "记录ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "卡券ID")
    @TableField("coupon_id")
    @NotNull(message = "卡券ID不能为空")
    private Integer couponId;

    @ApiModelProperty(value = "景区ID")
    @TableField("scenic_id")
    @NotBlank(message = "景区ID不能为空")
    private String scenicId;

    @ApiModelProperty(value = "用户ID")
    @TableField("user_id")
    @NotNull(message = "用户ID不能为空")
    private Integer userId;

    @ApiModelProperty(value = "使用状态：unused-未使用，used-已使用")
    @TableField("usage_status")
    @NotBlank(message = "使用状态不能为空")
    private String usageStatus;

    @ApiModelProperty(value = "使用时间")
    @TableField("used_at")
    private LocalDateTime usedAt;

    @ApiModelProperty(value = "有效期截止时间（使用后12小时内有效）")
    @TableField("valid_to")
    private LocalDateTime validTo;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}
