const distributorService = require('../../utils/distributorService');
const userService = require('../../utils/userService');

Page({
  data: {
    loading: false,
    distributorInfo: null,
    isDistributor: false,
    userInfo: null,
    // 显示数据
    displayData: {
      userName: '',
      avatarUrl: '',
      todayActivated: 0,
      totalCommission: '0.00',
      availableCommission: '0.00',
      withdrawnCommission: '0.00',
      unsettledCommission: '0.00',
      commissionRate: '0%',
      distributorCode: '',
      qrcodeUrl: ''
    }
  },

  onLoad: function (options) {
    console.log('分销中心页面加载');
    this.initPage();
  },

  onShow: function () {
    // 页面显示时刷新数据
    if (this.data.userInfo) {
      this.loadDistributorData();
    }
  },

  // 初始化页面
  async initPage() {
    try {
      this.setData({ loading: true });

      // 获取当前用户信息
      const userInfo = await this.getCurrentUser();
      if (!userInfo) {
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        });
        return;
      }

      this.setData({ userInfo });

      // 直接加载分销员数据，不进行身份验证
      await this.loadDistributorData();

    } catch (error) {
      console.error('初始化页面失败:', error);
      wx.showToast({
        title: '页面加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 获取当前用户信息
  async getCurrentUser() {
    try {
      // 先尝试从本地存储获取用户信息
      const storedUserInfo = wx.getStorageSync('userInfo');
      if (storedUserInfo && storedUserInfo.id) {
        return storedUserInfo;
      }

      // 如果没有本地用户信息，使用默认用户ID
      return {
        id: 1,
        name: '用户'
      };
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return {
        id: 1,
        name: '用户'
      };
    }
  },

  // 加载分销员数据
  async loadDistributorData() {
    try {
      const userId = this.data.userInfo.id;
      console.log('加载分销员数据，用户ID:', userId);

      // 获取分销员信息
      const distributorInfo = await distributorService.getDistributorByUserId(userId);

      if (distributorInfo) {
        // 用户是分销员，更新显示数据
        this.setData({
          distributorInfo,
          isDistributor: true
        });
        this.updateDisplayData(distributorInfo);
      } else {
        // 用户不是分销员
        this.setData({
          distributorInfo: null,
          isDistributor: false
        });
        this.updateDisplayDataForNonDistributor();
      }

    } catch (error) {
      console.error('加载分销员数据失败:', error);
      wx.showToast({
        title: '加载数据失败',
        icon: 'none'
      });
    }
  },

  // 更新显示数据（分销员）
  updateDisplayData(distributorInfo) {
    const globalUserInfo = getApp().globalData.userInfo || {};
    const displayData = {
      userName: globalUserInfo.nickName || globalUserInfo.nickname || '分销员',
      avatarUrl: globalUserInfo.avatarUrl || '',
      todayActivated: distributorInfo.todayActivated || 0,
      totalCommission: distributorService.formatAmount(distributorInfo.totalCommission),
      availableCommission: distributorService.formatAmount(distributorInfo.availableCommission),
      withdrawnCommission: distributorService.formatAmount(distributorInfo.withdrawnCommission),
      unsettledCommission: distributorService.formatAmount(distributorInfo.unsettledCommission),
      commissionRate: distributorService.formatCommissionRate(distributorInfo.commissionRate),
      distributorCode: distributorInfo.distributorCode || '',
      qrcodeUrl: distributorInfo.qrcodeUrl || ''
    };

    this.setData({
      displayData,
      userInfo: globalUserInfo
    });
    console.log('分销员显示数据更新:', displayData);
    console.log('用户信息更新:', globalUserInfo);
  },

  // 更新显示数据（非分销员）
  updateDisplayDataForNonDistributor() {
    const globalUserInfo = getApp().globalData.userInfo || {};
    const displayData = {
      userName: globalUserInfo.nickName || globalUserInfo.nickname || this.data.userInfo?.name || '用户',
      avatarUrl: globalUserInfo.avatarUrl || '',
      todayActivated: 0,
      totalCommission: '0.00',
      availableCommission: '0.00',
      withdrawnCommission: '0.00',
      unsettledCommission: '0.00',
      commissionRate: '0%',
      distributorCode: '',
      qrcodeUrl: ''
    };

    this.setData({
      displayData,
      userInfo: globalUserInfo
    });
    console.log('非分销员显示数据更新:', displayData);
    console.log('用户信息更新:', globalUserInfo);
  },

  // 提现操作
  async handleWithdraw() {
    const availableAmount = parseFloat(this.data.displayData.availableCommission);
    if (availableAmount <= 0) {
      wx.showToast({
        title: '暂无可提现金额',
        icon: 'none'
      });
      return;
    }

    // 跳转到提现页面
    wx.navigateTo({
      url: '/pages/withdrawal/component'
    });
  },

  // 分销订单页面
  goToDistributionOrders() {
    wx.navigateTo({
      url: '/pages/lanhu_fenxiaodingdan/component'
    });
  },

  // 提现明细页面
  goToWithdrawalRecords() {
    wx.navigateTo({
      url: '/pages/lanhu_tixianmingxi/component'
    });
  },

  // 规则说明页面
  goToRules() {
    wx.navigateTo({
      url: '/pages/distribution_rules/component'
    });
  },

  // 分销人员二维码页面
  goToQrCode() {
    if (!this.data.distributorInfo || !this.data.distributorInfo.id) {
      wx.showToast({
        title: '分销员信息不完整',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/distributor_qrcode/component?distributorId=${this.data.distributorInfo.id}`
    });
  },

  // 页面跳转处理（兼容原有的通用跳转）
  goTo(e) {
    console.log('页面跳转事件:', e);

    // 暂时显示提示
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  },

  // 返回上一级页面
  goBack() {
    console.log('返回上一级页面');
    wx.navigateBack({
      delta: 1
    });
  }
})
