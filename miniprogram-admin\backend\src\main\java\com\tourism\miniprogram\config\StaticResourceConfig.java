package com.tourism.miniprogram.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 静态资源配置
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Configuration
public class StaticResourceConfig implements WebMvcConfigurer {

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 配置小程序码图片访问路径
        registry.addResourceHandler("/uploads/miniprogram-codes/**")
                .addResourceLocations("file:uploads/miniprogram-codes/");
    }
}
