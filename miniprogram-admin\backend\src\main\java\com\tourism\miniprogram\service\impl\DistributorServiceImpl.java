package com.tourism.miniprogram.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tourism.miniprogram.entity.Distributor;
import com.tourism.miniprogram.mapper.DistributorMapper;
import com.tourism.miniprogram.service.DistributorService;
import com.tourism.miniprogram.service.WechatMiniProgramService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 分销员服务实现类
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@Service
public class DistributorServiceImpl extends ServiceImpl<DistributorMapper, Distributor> implements DistributorService {

    @Autowired
    private WechatMiniProgramService wechatMiniProgramService;

    @Override
    public Distributor getByUserId(Integer userId) {
        try {
            return baseMapper.selectByUserId(userId);
        } catch (Exception e) {
            log.error("根据用户ID获取分销员信息失败，userId: {}", userId, e);
            throw new RuntimeException("获取分销员信息失败");
        }
    }

    @Override
    public Distributor getByDistributorCode(String distributorCode) {
        try {
            return baseMapper.selectByDistributorCode(distributorCode);
        } catch (Exception e) {
            log.error("根据分销员编号获取分销员信息失败，distributorCode: {}", distributorCode, e);
            throw new RuntimeException("获取分销员信息失败");
        }
    }

    @Override
    public List<Distributor> getByStatus(String status) {
        try {
            return baseMapper.selectByStatus(status);
        } catch (Exception e) {
            log.error("根据状态获取分销员列表失败，status: {}", status, e);
            throw new RuntimeException("获取分销员列表失败");
        }
    }

    @Override
    @Transactional
    public Distributor createDistributor(Integer userId, BigDecimal commissionRate) {
        try {
            log.info("开始创建分销员，userId: {}, commissionRate: {}", userId, commissionRate);

            // 检查用户是否已是分销员
            Distributor existingDistributor = getByUserId(userId);
            if (existingDistributor != null) {
                log.warn("用户已是分销员，userId: {}", userId);
                throw new RuntimeException("用户已是分销员");
            }

            // 创建分销员
            Distributor distributor = new Distributor();
            distributor.setUserId(userId);
            distributor.setDistributorCode(generateDistributorCode());
            distributor.setTodayActivated(0);
            distributor.setTotalCommission(BigDecimal.ZERO);
            distributor.setAvailableCommission(BigDecimal.ZERO);
            distributor.setWithdrawnCommission(BigDecimal.ZERO);
            distributor.setUnsettledCommission(BigDecimal.ZERO);
            distributor.setStatus("active");
            distributor.setCommissionRate(commissionRate != null ? commissionRate : new BigDecimal("10.00"));
            distributor.setCustomRate(commissionRate != null);

            boolean success = save(distributor);
            if (success) {
                log.info("分销员创建成功，distributorId: {}, distributorCode: {}", 
                        distributor.getId(), distributor.getDistributorCode());
                return distributor;
            } else {
                log.error("分销员创建失败，数据库保存失败");
                throw new RuntimeException("分销员创建失败");
            }
        } catch (Exception e) {
            log.error("创建分销员失败，userId: {}", userId, e);
            throw new RuntimeException("创建分销员失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean updateStatus(Integer distributorId, String status) {
        try {
            log.info("更新分销员状态，distributorId: {}, status: {}", distributorId, status);

            Distributor distributor = getById(distributorId);
            if (distributor == null) {
                log.error("分销员不存在，distributorId: {}", distributorId);
                return false;
            }

            distributor.setStatus(status);
            boolean success = updateById(distributor);
            
            if (success) {
                log.info("分销员状态更新成功，distributorId: {}, newStatus: {}", distributorId, status);
            } else {
                log.error("分销员状态更新失败，distributorId: {}", distributorId);
            }
            
            return success;
        } catch (Exception e) {
            log.error("更新分销员状态失败，distributorId: {}, status: {}", distributorId, status, e);
            throw new RuntimeException("更新分销员状态失败");
        }
    }

    @Override
    @Transactional
    public boolean updateCommissionInfo(Integer distributorId, BigDecimal totalCommission, 
                                      BigDecimal availableCommission, BigDecimal unsettledCommission) {
        try {
            log.info("更新分销员佣金信息，distributorId: {}, totalCommission: {}, availableCommission: {}, unsettledCommission: {}", 
                    distributorId, totalCommission, availableCommission, unsettledCommission);

            int updatedRows = baseMapper.updateCommissionInfo(distributorId, totalCommission, 
                                                            availableCommission, unsettledCommission);
            
            if (updatedRows > 0) {
                log.info("分销员佣金信息更新成功，distributorId: {}", distributorId);
                return true;
            } else {
                log.error("分销员佣金信息更新失败，distributorId: {}", distributorId);
                return false;
            }
        } catch (Exception e) {
            log.error("更新分销员佣金信息失败，distributorId: {}", distributorId, e);
            throw new RuntimeException("更新分销员佣金信息失败");
        }
    }

    @Override
    @Transactional
    public boolean updateTodayActivated(Integer distributorId, Integer todayActivated) {
        try {
            log.info("更新分销员今日激活人数，distributorId: {}, todayActivated: {}", distributorId, todayActivated);

            int updatedRows = baseMapper.updateTodayActivated(distributorId, todayActivated);
            
            if (updatedRows > 0) {
                log.info("分销员今日激活人数更新成功，distributorId: {}", distributorId);
                return true;
            } else {
                log.error("分销员今日激活人数更新失败，distributorId: {}", distributorId);
                return false;
            }
        } catch (Exception e) {
            log.error("更新分销员今日激活人数失败，distributorId: {}", distributorId, e);
            throw new RuntimeException("更新分销员今日激活人数失败");
        }
    }

    @Override
    @Transactional
    public boolean resetTodayActivated() {
        try {
            log.info("重置所有分销员今日激活人数");

            int updatedRows = baseMapper.resetTodayActivated();
            
            log.info("重置分销员今日激活人数完成，影响行数: {}", updatedRows);
            return true;
        } catch (Exception e) {
            log.error("重置分销员今日激活人数失败", e);
            throw new RuntimeException("重置分销员今日激活人数失败");
        }
    }

    @Override
    public String generateDistributorCode() {
        // 生成格式：DIST + 时间戳后8位 + 随机4位数字
        String timestamp = String.valueOf(System.currentTimeMillis());
        String randomNum = String.format("%04d", (int)(Math.random() * 10000));
        return "DIST" + timestamp.substring(timestamp.length() - 8) + randomNum;
    }

    @Override
    public String generateQrCode(Integer distributorId) {
        try {
            log.info("生成分销员专属小程序码，distributorId: {}", distributorId);

            Distributor distributor = getById(distributorId);
            if (distributor == null) {
                log.error("分销员不存在，distributorId: {}", distributorId);
                throw new RuntimeException("分销员不存在");
            }

            // 生成小程序码URL
            String miniProgramCodeUrl = generateMiniProgramCode(distributorId);

            // 更新分销员的小程序码URL
            distributor.setQrcodeUrl(miniProgramCodeUrl);
            updateById(distributor);

            log.info("分销员专属小程序码生成成功，distributorId: {}, qrCodeUrl: {}", distributorId, miniProgramCodeUrl);
            return miniProgramCodeUrl;
        } catch (Exception e) {
            log.error("生成分销员专属小程序码失败，distributorId: {}", distributorId, e);
            throw new RuntimeException("生成专属小程序码失败: " + e.getMessage());
        }
    }

    @Override
    public Integer getActiveDistributorCount() {
        try {
            return baseMapper.countActiveDistributors();
        } catch (Exception e) {
            log.error("获取活跃分销员数量失败", e);
            throw new RuntimeException("获取活跃分销员数量失败");
        }
    }

    @Override
    public BigDecimal getTotalCommissionSum() {
        try {
            return baseMapper.getTotalCommissionSum();
        } catch (Exception e) {
            log.error("获取分销员总佣金统计失败", e);
            throw new RuntimeException("获取分销员总佣金统计失败");
        }
    }

    @Override
    public boolean isDistributor(Integer userId) {
        try {
            Distributor distributor = getByUserId(userId);
            return distributor != null;
        } catch (Exception e) {
            log.error("检查用户是否为分销员失败，userId: {}", userId, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean freezeDistributor(Integer distributorId, String reason) {
        try {
            log.info("冻结分销员，distributorId: {}, reason: {}", distributorId, reason);

            boolean success = updateStatus(distributorId, "frozen");
            
            if (success) {
                log.info("分销员冻结成功，distributorId: {}", distributorId);
            }
            
            return success;
        } catch (Exception e) {
            log.error("冻结分销员失败，distributorId: {}", distributorId, e);
            throw new RuntimeException("冻结分销员失败");
        }
    }

    @Override
    @Transactional
    public boolean unfreezeDistributor(Integer distributorId) {
        try {
            log.info("解冻分销员，distributorId: {}", distributorId);

            boolean success = updateStatus(distributorId, "active");
            
            if (success) {
                log.info("分销员解冻成功，distributorId: {}", distributorId);
            }
            
            return success;
        } catch (Exception e) {
            log.error("解冻分销员失败，distributorId: {}", distributorId, e);
            throw new RuntimeException("解冻分销员失败");
        }
    }

    @Override
    public String generateMiniProgramCode(Integer distributorId) {
        try {
            log.info("生成分销员专属小程序码，distributorId: {}", distributorId);

            Distributor distributor = getById(distributorId);
            if (distributor == null) {
                log.error("分销员不存在，distributorId: {}", distributorId);
                throw new RuntimeException("分销员不存在");
            }

            // 构建场景值，包含分销员编号
            String scene = "distributor=" + distributor.getDistributorCode();

            // 指定小程序页面路径（需要根据实际小程序页面调整）
            String page = "pages/lanhu_shouye/component";

            // 生成小程序码
            byte[] miniProgramCodeBytes = wechatMiniProgramService.generateMiniProgramCode(scene, page, 430);

            // 生成文件名
            String fileName = "distributor_" + distributor.getDistributorCode() + "_" + System.currentTimeMillis() + ".png";

            // 上传到云存储并获取URL
            String miniProgramCodeUrl = wechatMiniProgramService.uploadMiniProgramCodeToCloud(miniProgramCodeBytes, fileName);

            log.info("分销员专属小程序码生成成功，distributorId: {}, url: {}", distributorId, miniProgramCodeUrl);
            return miniProgramCodeUrl;
        } catch (Exception e) {
            log.error("生成分销员专属小程序码失败，distributorId: {}", distributorId, e);
            throw new RuntimeException("生成专属小程序码失败: " + e.getMessage());
        }
    }
}
