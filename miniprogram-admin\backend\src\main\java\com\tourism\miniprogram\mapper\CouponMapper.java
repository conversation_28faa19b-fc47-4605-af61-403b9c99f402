package com.tourism.miniprogram.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tourism.miniprogram.entity.Coupon;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 数字门票Mapper接口
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Mapper
public interface CouponMapper extends BaseMapper<Coupon> {

    /**
     * 根据用户ID获取门票列表
     *
     * @param userId 用户ID
     * @return 门票列表
     */
    @Select("SELECT * FROM coupons WHERE user_id = #{userId} ORDER BY created_at DESC")
    List<Coupon> selectCouponsByUserId(Integer userId);

    /**
     * 根据景区ID获取门票列表（通过产品关联）
     *
     * @param scenicId 景区ID
     * @return 门票列表
     */
    @Select("SELECT c.* FROM coupons c " +
            "JOIN products p ON c.product_id = p.id " +
            "WHERE p.scenic_id = #{scenicId} " +
            "ORDER BY c.created_at DESC")
    List<Coupon> selectCouponsByScenicId(String scenicId);

    /**
     * 根据状态获取门票列表
     *
     * @param status 门票状态
     * @return 门票列表
     */
    @Select("SELECT * FROM coupons WHERE status = #{status} ORDER BY created_at DESC")
    List<Coupon> selectCouponsByStatus(String status);

    /**
     * 根据用户ID和景区ID获取有效门票（通过产品关联）
     *
     * @param userId 用户ID
     * @param scenicId 景区ID
     * @return 门票列表
     */
    @Select("SELECT c.* FROM coupons c " +
            "JOIN products p ON c.product_id = p.id " +
            "WHERE c.user_id = #{userId} " +
            "AND p.scenic_id = #{scenicId} " +
            "AND c.status IN ('active', 'unactivated') " +
            "ORDER BY c.created_at DESC")
    List<Coupon> selectValidCouponsByUserAndScenic(Integer userId, String scenicId);

    /**
     * 根据用户ID和景区ID查询未激活的数字门票（通过产品关联）
     * 支持单品和组合包产品：
     * - 单品：直接匹配 scenic_id
     * - 组合包：使用 FIND_IN_SET 查找景区ID是否在逗号分隔的字符串中
     *
     * @param userId 用户ID
     * @param scenicId 景区ID
     * @return 未激活的门票列表
     */
    @Select("SELECT c.* FROM coupons c " +
            "JOIN products p ON c.product_id = p.id " +
            "WHERE c.user_id = #{userId} " +
            "AND (p.scenic_id = #{scenicId} OR FIND_IN_SET(#{scenicId}, p.scenic_id) > 0) " +
            "AND c.status = 'unactivated'")
    List<Coupon> selectUnactivatedCouponsByUserAndScenic(@Param("userId") Integer userId,
                                                         @Param("scenicId") String scenicId);

    /**
     * 根据用户ID和产品ID查询未激活的数字门票
     *
     * @param userId 用户ID
     * @param productId 产品ID
     * @return 未激活的门票列表
     */
    @Select("SELECT * FROM coupons WHERE user_id = #{userId} AND product_id = #{productId} AND status = 'unactivated'")
    List<Coupon> selectUnactivatedCouponsByUserAndProduct(@Param("userId") Integer userId,
                                                          @Param("productId") String productId);

    /**
     * 根据用户ID和景区ID查询已激活的数字门票（通过产品关联）
     * 支持单品和组合包产品：
     * - 单品：直接匹配 scenic_id
     * - 组合包：使用 FIND_IN_SET 查找景区ID是否在逗号分隔的字符串中
     *
     * @param userId 用户ID
     * @param scenicId 景区ID
     * @return 已激活的门票列表
     */
    @Select("SELECT c.* FROM coupons c " +
            "JOIN products p ON c.product_id = p.id " +
            "WHERE c.user_id = #{userId} " +
            "AND (p.scenic_id = #{scenicId} OR FIND_IN_SET(#{scenicId}, p.scenic_id) > 0) " +
            "AND c.status = 'active'")
    List<Coupon> selectActivatedCouponsByUserAndScenic(@Param("userId") Integer userId,
                                                       @Param("scenicId") String scenicId);

    /**
     * 根据用户ID和产品ID查询已激活的数字门票
     *
     * @param userId 用户ID
     * @param productId 产品ID
     * @return 已激活的门票列表
     */
    @Select("SELECT * FROM coupons WHERE user_id = #{userId} AND product_id = #{productId} AND status = 'active'")
    List<Coupon> selectActivatedCouponsByUserAndProduct(@Param("userId") Integer userId,
                                                        @Param("productId") String productId);

    /**
     * 查询满足过期条件的数字门票
     * 条件：used_at不为null（已被使用）且valid_to小于当前时间（已过期）且status不等于'used'（避免重复更新）
     *
     * @return 过期的门票列表
     */
    @Select("SELECT * FROM coupons WHERE used_at IS NOT NULL AND valid_to < NOW() AND status != 'used'")
    List<Coupon> selectExpiredCoupons();

    /**
     * 批量更新过期卡券状态为'used'
     *
     * @param couponIds 卡券ID列表
     * @return 更新的记录数
     */
    @Update("<script>" +
            "UPDATE coupons SET status = 'used', updated_at = NOW() " +
            "WHERE id IN " +
            "<foreach collection='couponIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    int batchUpdateExpiredCouponsStatus(@Param("couponIds") List<Integer> couponIds);

    /**
     * 根据产品ID查询产品类型
     *
     * @param productId 产品ID
     * @return 产品类型
     */
    @Select("SELECT product_type FROM products WHERE id = #{productId}")
    String selectProductType(@Param("productId") Integer productId);
}
