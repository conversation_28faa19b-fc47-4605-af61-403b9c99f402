.page {
  background-color: rgba(239, 234, 228, 1.000000);
  position: relative;
  width: 750rpx;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding-bottom: 120rpx;
  /* 为底部固定按钮留出空间 */
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #ff6b35;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  margin-top: 20rpx;
  color: #666;
  font-size: 28rpx;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  padding: 40rpx;
}

.error-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.error-text {
  color: #ff4444;
  font-size: 28rpx;
  text-align: center;
  margin-bottom: 30rpx;
}

.retry-btn {
  background-color: #ff6b35;
  color: white;
  border: none;
  border-radius: 40rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 讲解包内容 */
.bundle-content {
  flex: 1;
  width: 100%;
}

.bundle-long-image {
  width: 100%;
  display: block;
}

.no-image {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  background-color: #f5f5f5;
  color: #999;
  font-size: 28rpx;
}

/* 底部固定悬浮购买按钮 */
.fixed-buy-bar {
  position: fixed;
  bottom: 30rpx;
  left: 30rpx;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  z-index: 1000;
  width: 690rpx;
  height: 120rpx;
  background: rgba(0, 0, 0, 1); /* 完全透明 */
  border-radius: 200rpx 200rpx 200rpx 200rpx;
}

.price-info {
  display: flex;
  align-items: baseline;
  color: white;
  margin-top: 15rpx;
  margin-left: 30rpx;
}

.discount-label {
  background-color: linear-gradient( 90deg, #EE864E 0%, #ED6455 100%);
  color: #EFEAE4;
  font-size: 20rpx;
  border-radius: 8rpx;
  width: 258rpx;
  height: 40rpx;
  margin-bottom: 10rpx;
}

.price-text {
  font-size: 48rpx;
  font-weight: bold;
  color: white;
}

.price-unit {
  font-size: 24rpx;
  color: white;
  margin-left: 4rpx;
}

.buy-button {
  background-color:#FFBC47;
  color: black;
  border: none;
  left: 110rpx;
  border-radius: 0 200rpx 200rpx 0;
  clip-path: polygon(20% 0, 100% 0, 100% 100%, 0 100%);
  padding: 40rpx 40rpx;
  font-size: 32rpx;
  font-weight: bold;
  min-width: 160rpx;
  text-align: center;
  z-index: 1001;
}

.buy-button-active {
  background-color:#FFBC47;
}

.buy-button-disabled {
  background-color:#FFBC47;
  color: white;
}

.text-wrapper_5 {
  height: 40rpx;
  background: linear-gradient( 90deg, #EE854E 0%, #EC6555 100%);
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;
  width: 250rpx;
  position: absolute;
  left: 6rpx;
  top: -10rpx;
  border-radius: 50rpx 50rpx 50rpx 0rpx;
}
.text_12 {
  width: 216rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgba(255,255,255,1.000000);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 36rpx;
  margin: 2rpx 0 0 16rpx;
}