package com.tourism.miniprogram.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tourism.miniprogram.entity.CouponUsageRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 卡券使用记录Mapper接口
 *
 * <AUTHOR> Team
 * @since 2025-06-19
 */
@Mapper
public interface CouponUsageRecordMapper extends BaseMapper<CouponUsageRecord> {

    /**
     * 根据卡券ID获取使用记录列表
     *
     * @param couponId 卡券ID
     * @return 使用记录列表
     */
    @Select("SELECT * FROM coupon_usage_records WHERE coupon_id = #{couponId} ORDER BY created_at DESC")
    List<CouponUsageRecord> selectRecordsByCouponId(Integer couponId);

    /**
     * 根据卡券编码获取使用记录列表
     *
     * @param couponCode 卡券编码
     * @return 使用记录列表
     */
    @Select("SELECT cur.* FROM coupon_usage_records cur " +
            "JOIN coupons c ON cur.coupon_id = c.id " +
            "WHERE c.code = #{couponCode} ORDER BY cur.created_at DESC")
    List<CouponUsageRecord> selectRecordsByCouponCode(String couponCode);

    /**
     * 根据用户ID获取使用记录列表
     *
     * @param userId 用户ID
     * @return 使用记录列表
     */
    @Select("SELECT * FROM coupon_usage_records WHERE user_id = #{userId} ORDER BY created_at DESC")
    List<CouponUsageRecord> selectRecordsByUserId(Integer userId);

    /**
     * 根据景区ID获取使用记录列表
     *
     * @param scenicId 景区ID
     * @return 使用记录列表
     */
    @Select("SELECT * FROM coupon_usage_records WHERE scenic_id = #{scenicId} ORDER BY created_at DESC")
    List<CouponUsageRecord> selectRecordsByScenicId(String scenicId);

    /**
     * 根据卡券ID和景区ID获取使用记录
     *
     * @param couponId 卡券ID
     * @param scenicId 景区ID
     * @return 使用记录
     */
    @Select("SELECT * FROM coupon_usage_records WHERE coupon_id = #{couponId} AND scenic_id = #{scenicId}")
    CouponUsageRecord selectRecordByCouponAndScenic(@Param("couponId") Integer couponId, 
                                                    @Param("scenicId") String scenicId);

    /**
     * 根据用户ID和景区ID获取使用记录列表
     *
     * @param userId 用户ID
     * @param scenicId 景区ID
     * @return 使用记录列表
     */
    @Select("SELECT * FROM coupon_usage_records WHERE user_id = #{userId} AND scenic_id = #{scenicId} ORDER BY created_at DESC")
    List<CouponUsageRecord> selectRecordsByUserAndScenic(@Param("userId") Integer userId, 
                                                         @Param("scenicId") String scenicId);

    /**
     * 根据使用状态获取记录列表
     *
     * @param usageStatus 使用状态
     * @return 使用记录列表
     */
    @Select("SELECT * FROM coupon_usage_records WHERE usage_status = #{usageStatus} ORDER BY created_at DESC")
    List<CouponUsageRecord> selectRecordsByStatus(String usageStatus);

    /**
     * 更新使用记录状态为激活状态
     * 设置 usage_status = 'active', valid_to = NOW() + 12小时
     *
     * @param couponId 卡券ID
     * @param scenicId 景区ID
     * @return 更新的记录数
     */
    @Update("UPDATE coupon_usage_records SET usage_status = 'active', " +
            "valid_to = DATE_ADD(NOW(), INTERVAL 12 HOUR), updated_at = NOW() " +
            "WHERE coupon_id = #{couponId} AND scenic_id = #{scenicId} AND usage_status = 'unactivated'")
    int updateUsageStatusToActive(@Param("couponId") Integer couponId, @Param("scenicId") String scenicId);

    /**
     * 更新使用记录状态为已使用
     * 设置 usage_status = 'used', used_at = NOW(), valid_to = NOW() + 12小时
     *
     * @param couponId 卡券ID
     * @param scenicId 景区ID
     * @return 更新的记录数
     */
    @Update("UPDATE coupon_usage_records SET usage_status = 'used', used_at = NOW(), " +
            "valid_to = DATE_ADD(NOW(), INTERVAL 12 HOUR), updated_at = NOW() " +
            "WHERE coupon_id = #{couponId} AND scenic_id = #{scenicId} AND usage_status IN ('unactivated', 'active')")
    int updateUsageStatusToUsed(@Param("couponId") Integer couponId, @Param("scenicId") String scenicId);

    /**
     * 批量插入使用记录
     *
     * @param records 使用记录列表
     * @return 插入的记录数
     */
    @Select("<script>" +
            "INSERT INTO coupon_usage_records (coupon_id, scenic_id, user_id, usage_status, created_at, updated_at) VALUES " +
            "<foreach collection='records' item='record' separator=','>" +
            "(#{record.couponId}, #{record.scenicId}, #{record.userId}, #{record.usageStatus}, NOW(), NOW())" +
            "</foreach>" +
            "</script>")
    int batchInsertRecords(@Param("records") List<CouponUsageRecord> records);

    /**
     * 查询满足过期条件的卡券使用记录
     * 条件：valid_to不为null且valid_to小于当前时间（已过期）且usage_status不等于'used'（避免重复更新）
     *
     * @return 过期的使用记录列表
     */
    @Select("SELECT * FROM coupon_usage_records WHERE valid_to IS NOT NULL AND valid_to < NOW() AND usage_status != 'used'")
    List<CouponUsageRecord> selectExpiredUsageRecords();

    /**
     * 批量更新过期使用记录状态为'used'
     *
     * @param recordIds 使用记录ID列表
     * @return 更新的记录数
     */
    @Update("<script>" +
            "UPDATE coupon_usage_records SET usage_status = 'used', updated_at = NOW() " +
            "WHERE id IN " +
            "<foreach collection='recordIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    int batchUpdateExpiredUsageRecordsStatus(@Param("recordIds") List<Integer> recordIds);

    /**
     * 统计卡券在各景区的使用情况
     *
     * @param couponId 卡券ID
     * @return 使用统计列表
     */
    @Select("SELECT scenic_id, usage_status, COUNT(*) as count " +
            "FROM coupon_usage_records " +
            "WHERE coupon_id = #{couponId} " +
            "GROUP BY scenic_id, usage_status " +
            "ORDER BY scenic_id")
    List<CouponUsageRecord> selectUsageStatsByCouponId(Integer couponId);
}
