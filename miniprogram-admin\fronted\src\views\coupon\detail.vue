<template>
  <div class="coupon-detail">
    <!-- 页面头部 -->
    <div class="page-header">
      <el-button @click="handleBack" type="info" plain>
        <el-icon><ArrowLeft /></el-icon>
        返回列表
      </el-button>
      <h2>门票详情</h2>
    </div>

    <div v-loading="loading" class="detail-content">
      <!-- 门票基本信息 -->
      <el-card class="coupon-info-card">
        <template #header>
          <div class="card-header">
            <span>门票信息</span>
            <div>
              <el-tag :type="getStatusType(detail.status)" size="large">
                {{ getStatusText(detail.status) }}
              </el-tag>
            </div>
          </div>
        </template>

        <div class="info-grid">
          <div class="info-item">
            <label>门票ID：</label>
            <span>{{ detail.id }}</span>
          </div>
          <div class="info-item">
            <label>卡券编码：</label>
            <span>{{ detail.code }}</span>
          </div>
          <div class="info-item">
            <label>用户ID：</label>
            <span>{{ detail.userId }}</span>
          </div>
          <div class="info-item">
            <label>景区ID：</label>
            <span>{{ detail.scenicId }}</span>
          </div>
          <div class="info-item">
            <label>产品ID：</label>
            <span>{{ detail.productId || '-' }}</span>
          </div>
          <div class="info-item">
            <label>组合包ID：</label>
            <span>{{ detail.bundleId || '-' }}</span>
          </div>
          <div class="info-item">
            <label>订单ID：</label>
            <span>{{ detail.orderId }}</span>
          </div>
          <div class="info-item">
            <label>激活码ID：</label>
            <span>{{ detail.activationCodeId || '-' }}</span>
          </div>
          <div class="info-item">
            <label>生效时间：</label>
            <span>{{ formatDate(detail.validFrom) || '-' }}</span>
          </div>
          <div class="info-item">
            <label>过期时间：</label>
            <span>{{ formatDate(detail.validTo) || '-' }}</span>
          </div>
          <div class="info-item">
            <label>使用时间：</label>
            <span>{{ formatDate(detail.usedAt) || '-' }}</span>
          </div>
          <div class="info-item">
            <label>创建时间：</label>
            <span>{{ formatDate(detail.createdAt) }}</span>
          </div>
        </div>
      </el-card>

      <!-- 订单详细信息 -->
      <el-card v-if="orderInfo.id" class="order-info-card">
        <template #header>
          <div class="card-header">
            <span>关联订单信息</span>
            <div>
              <el-tag :type="getOrderStatusType(orderInfo.status)" size="small">
                {{ getOrderStatusText(orderInfo.status) }}
              </el-tag>
            </div>
          </div>
        </template>

        <div class="order-info-section">
          <div class="info-grid">
            <div class="info-item">
              <label>订单编号：</label>
              <span>{{ orderInfo.orderNo }}</span>
            </div>
            <div class="info-item">
              <label>订单金额：</label>
              <span class="money-text">¥{{ formatMoney(orderInfo.totalAmount) }}</span>
            </div>
            <div class="info-item">
              <label>用户ID：</label>
              <span>{{ orderInfo.userId }}</span>
            </div>
            <div class="info-item">
              <label>订单状态：</label>
              <el-tag :type="getOrderStatusType(orderInfo.status)" size="small">
                {{ getOrderStatusText(orderInfo.status) }}
              </el-tag>
            </div>
            <div class="info-item">
              <label>创建时间：</label>
              <span>{{ formatDate(orderInfo.createdAt) }}</span>
            </div>
            <div class="info-item">
              <label>更新时间：</label>
              <span>{{ formatDate(orderInfo.updatedAt) || '-' }}</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 产品详细信息 -->
      <el-card v-if="productInfo.id" class="product-info-card">
        <template #header>
          <div class="card-header">
            <span>关联产品信息</span>
            <div>
              <el-tag :type="productInfo.status === 1 ? 'success' : 'danger'" size="small">
                {{ productInfo.status === 1 ? '启用' : '禁用' }}
              </el-tag>
            </div>
          </div>
        </template>

        <div class="product-content">
          <div class="product-main-info">
            <div class="product-details">
              <h3>{{ productInfo.name }}</h3>
              <p class="product-description">{{ productInfo.description }}</p>
              <div class="product-meta">
                <div class="meta-item">
                  <label>产品类型：</label>
                  <span>{{ getProductTypeText(productInfo.productType) }}</span>
                </div>
                <div class="meta-item">
                  <label>价格：</label>
                  <span class="price-text">¥{{ formatMoney(productInfo.price) }}</span>
                </div>
                <div class="meta-item">
                  <label>景区ID：</label>
                  <span>{{ productInfo.scenicId || '-' }}</span>
                </div>
                <div class="meta-item">
                  <label>有效期：</label>
                  <span>{{ productInfo.validityHours ? productInfo.validityHours + '小时' : '-' }}</span>
                </div>
              </div>
            </div>
          </div>

          <div class="product-additional-info">
            <div class="info-grid">
              <div class="info-item">
                <label>产品ID：</label>
                <span>{{ productInfo.id }}</span>
              </div>
              <div class="info-item">
                <label>需要激活：</label>
                <span>{{ productInfo.requireActivation ? '是' : '否' }}</span>
              </div>
              <div class="info-item">
                <label>关联订单：</label>
                <span>{{ productInfo.orderId || '-' }}</span>
              </div>
              <div class="info-item">
                <label>创建时间：</label>
                <span>{{ formatDate(productInfo.createdAt) }}</span>
              </div>
              <div class="info-item">
                <label>更新时间：</label>
                <span>{{ formatDate(productInfo.updatedAt) || '-' }}</span>
              </div>
              <div class="info-item">
                <label>状态：</label>
                <el-tag :type="productInfo.status === 1 ? 'success' : 'danger'" size="small">
                  {{ productInfo.status === 1 ? '启用' : '禁用' }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 组合包详细信息 -->
      <el-card v-if="bundleInfo.id" class="bundle-info-card">
        <template #header>
          <div class="card-header">
            <span>关联组合包信息</span>
            <div>
              <el-tag type="success" size="small">组合包</el-tag>
            </div>
          </div>
        </template>

        <div class="bundle-content">
          <div class="bundle-main-info">
            <div v-if="bundleInfo.imageUrl" class="bundle-image">
              <img :src="bundleInfo.imageUrl" :alt="bundleInfo.name" />
            </div>
            <div class="bundle-details">
              <h3>{{ bundleInfo.name }}</h3>
              <p class="bundle-description">{{ bundleInfo.description }}</p>
              <div class="bundle-meta">
                <div class="meta-item">
                  <label>组合包价格：</label>
                  <span class="price-text">¥{{ formatMoney(bundleInfo.totalPrice) }}</span>
                </div>
                <div class="meta-item">
                  <label>包含产品数：</label>
                  <span>{{ bundleInfo.productCount || 0 }}个</span>
                </div>
                <div class="meta-item">
                  <label>有效期：</label>
                  <span>{{ bundleInfo.validityPeriod || '-' }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 组合包包含的产品列表 -->
          <div v-if="bundleProducts.length > 0" class="bundle-products">
            <h4>包含产品</h4>
            <el-table :data="bundleProducts" style="width: 100%" stripe border>
              <el-table-column prop="productName" label="产品名称" min-width="200" />
              <el-table-column prop="productType" label="类型" width="100" align="center">
                <template #default="scope">
                  <el-tag :type="scope.row.productType === 'ticket' ? 'primary' : 'success'" size="small">
                    {{ getProductTypeText(scope.row.productType) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="quantity" label="数量" width="80" align="center" />
              <el-table-column prop="unitPrice" label="单价" width="120" align="right">
                <template #default="scope">
                  <span class="money-text">¥{{ formatMoney(scope.row.unitPrice) }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="subtotal" label="小计" width="120" align="right">
                <template #default="scope">
                  <span class="money-text">¥{{ formatMoney(scope.row.subtotal) }}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-card>

      <!-- 分销信息 -->
      <el-card v-if="distributionInfo" class="distribution-card">
        <template #header>
          <span>分销信息</span>
        </template>

        <div class="distribution-info">
          <div class="info-item">
            <label>分销员ID：</label>
            <span>{{ distributionInfo.distributorId }}</span>
          </div>
          <div class="info-item">
            <label>分销员编号：</label>
            <span>{{ distributionInfo.distributorCode }}</span>
          </div>
          <div class="info-item">
            <label>佣金比例：</label>
            <span>{{ distributionInfo.commissionRate }}%</span>
          </div>
          <div class="info-item">
            <label>佣金金额：</label>
            <span class="money-text commission">¥{{ formatMoney(distributionInfo.commissionAmount) }}</span>
          </div>
          <div class="info-item">
            <label>结算状态：</label>
            <el-tag :type="getDistributionStatusType(distributionInfo.status)" size="small">
              {{ getDistributionStatusText(distributionInfo.status) }}
            </el-tag>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getCouponById } from '@/api/coupon'
import { getOrderById } from '@/api/order'
import { getProductById } from '@/api/product'
import { getProductBundleById } from '@/api/productBundle'
import { checkDistributionRecord } from '@/api/distributor'
import { formatDate, formatMoney } from '@/utils'

const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const detail = reactive({})
const orderInfo = ref({})
const productInfo = ref({})
const bundleInfo = ref({})
const bundleProducts = ref([])
const distributionInfo = ref(null)

// 状态相关方法
const getStatusType = (status) => {
  const statusMap = {
    'unactivated': 'info',
    'active': 'success',
    'used': 'warning',
    'expired': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'unactivated': '未激活',
    'active': '已激活',
    'used': '已使用',
    'expired': '已过期'
  }
  return statusMap[status] || status
}

const getOrderStatusType = (status) => {
  const types = {
    pending: 'warning',
    paid: 'primary',
    completed: 'success',
    cancelled: 'danger'
  }
  return types[status] || 'info'
}

const getOrderStatusText = (status) => {
  const texts = {
    pending: '待支付',
    paid: '已支付',
    completed: '已完成',
    cancelled: '已取消'
  }
  return texts[status] || '未知'
}

const getProductTypeText = (type) => {
  const types = {
    ticket: '门票',
    guide: '讲解',
    package: '套餐',
    service: '服务'
  }
  return types[type] || type
}

const getDistributionStatusType = (status) => {
  const types = {
    pending: 'warning',
    settled: 'success',
    canceled: 'danger'
  }
  return types[status] || 'info'
}

const getDistributionStatusText = (status) => {
  const texts = {
    pending: '待结算',
    settled: '已结算',
    canceled: '已取消'
  }
  return texts[status] || '未知'
}

// 获取门票详情
const fetchDetail = async () => {
  try {
    loading.value = true
    const { data } = await getCouponById(route.params.id)
    Object.assign(detail, data)

    // 获取关联的订单、产品、组合包信息
    await Promise.all([
      fetchOrderInfo(),
      fetchProductInfo(),
      fetchBundleInfo(),
      fetchDistributionInfo()
    ])
  } catch (error) {
    ElMessage.error('获取门票详情失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 获取订单信息
const fetchOrderInfo = async () => {
  if (!detail.orderId) return

  try {
    const response = await getOrderById(detail.orderId)
    if (response.code === 200) {
      orderInfo.value = response.data
    }
  } catch (error) {
    console.error('获取订单信息失败:', error)
  }
}

// 获取产品信息
const fetchProductInfo = async () => {
  if (!detail.productId) return

  try {
    const response = await getProductById(detail.productId)
    if (response.code === 200) {
      productInfo.value = response.data
    }
  } catch (error) {
    console.error('获取产品信息失败:', error)
  }
}

// 获取组合包信息
const fetchBundleInfo = async () => {
  if (!detail.bundleId) return

  try {
    const response = await getProductBundleById(detail.bundleId)
    if (response.code === 200) {
      bundleInfo.value = response.data

      // 获取组合包包含的产品列表
      if (response.data.products) {
        bundleProducts.value = response.data.products
      }
    }
  } catch (error) {
    console.error('获取组合包信息失败:', error)
  }
}

// 获取分销信息
const fetchDistributionInfo = async () => {
  if (!detail.orderId) return

  try {
    const response = await checkDistributionRecord(detail.orderId)
    if (response.code === 200 && response.data) {
      // 如果存在分销记录，获取详细信息
      distributionInfo.value = {
        distributorId: response.data.distributorId,
        distributorCode: response.data.distributorCode,
        commissionRate: response.data.commissionRate,
        commissionAmount: response.data.commissionAmount,
        status: response.data.status
      }
    }
  } catch (error) {
    console.error('获取分销信息失败:', error)
  }
}

// 返回列表
const handleBack = () => {
  router.push('/coupon')
}

// 初始化
onMounted(() => {
  fetchDetail()
})
</script>

<style lang="scss" scoped>
.coupon-detail {
  .page-header {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 20px;

    h2 {
      margin: 0;
      color: #303133;
    }
  }

  .detail-content {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .coupon-info-card {
      margin-bottom: 20px;

      .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 15px;

        .info-item {
          display: flex;
          align-items: center;

          label {
            font-weight: 500;
            color: #606266;
            margin-right: 10px;
            min-width: 100px;
          }
        }
      }
    }

    .order-info-card {
      margin-bottom: 20px;

      .order-info-section {
        .info-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 15px;

          .info-item {
            display: flex;
            align-items: center;

            label {
              font-weight: 500;
              color: #606266;
              margin-right: 10px;
              min-width: 100px;
            }

            .money-text {
              font-weight: 600;
              color: #67C23A;
            }
          }
        }
      }
    }

    .product-info-card {
      margin-bottom: 20px;

      .product-content {
        .product-main-info {
          display: flex;
          gap: 20px;
          margin-bottom: 20px;

          .product-image {
            flex-shrink: 0;

            img {
              width: 150px;
              height: 150px;
              object-fit: cover;
              border-radius: 8px;
              border: 1px solid #dcdfe6;
            }
          }

          .product-details {
            flex: 1;

            h3 {
              margin: 0 0 10px 0;
              color: #303133;
              font-size: 18px;
            }

            .product-description {
              color: #606266;
              margin-bottom: 15px;
              line-height: 1.6;
            }

            .product-meta {
              .meta-item {
                display: flex;
                align-items: center;
                margin-bottom: 8px;

                label {
                  font-weight: 500;
                  color: #606266;
                  margin-right: 10px;
                  min-width: 80px;
                }

                .price-text {
                  font-weight: 600;
                  color: #E6A23C;
                  font-size: 16px;
                }
              }
            }
          }
        }

        .product-additional-info {
          .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;

            .info-item {
              display: flex;
              align-items: flex-start;

              label {
                font-weight: 500;
                color: #606266;
                margin-right: 10px;
                min-width: 100px;
                flex-shrink: 0;
              }
            }
          }
        }
      }
    }

    .bundle-info-card {
      margin-bottom: 20px;

      .bundle-content {
        .bundle-main-info {
          display: flex;
          gap: 20px;
          margin-bottom: 20px;

          .bundle-image {
            flex-shrink: 0;

            img {
              width: 150px;
              height: 150px;
              object-fit: cover;
              border-radius: 8px;
              border: 1px solid #dcdfe6;
            }
          }

          .bundle-details {
            flex: 1;

            h3 {
              margin: 0 0 10px 0;
              color: #303133;
              font-size: 18px;
            }

            .bundle-description {
              color: #606266;
              margin-bottom: 15px;
              line-height: 1.6;
            }

            .bundle-meta {
              .meta-item {
                display: flex;
                align-items: center;
                margin-bottom: 8px;

                label {
                  font-weight: 500;
                  color: #606266;
                  margin-right: 10px;
                  min-width: 100px;
                }

                .price-text {
                  font-weight: 600;
                  color: #E6A23C;
                  font-size: 16px;
                }
              }
            }
          }
        }

        .bundle-products {
          h4 {
            margin: 0 0 15px 0;
            color: #409EFF;
            font-size: 16px;
            border-bottom: 1px solid #ebeef5;
            padding-bottom: 8px;
          }

          .money-text {
            font-weight: 500;
            color: #67C23A;
          }
        }
      }
    }

    .distribution-card {
      margin-bottom: 20px;

      .distribution-info {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 15px;

        .info-item {
          display: flex;
          align-items: center;

          label {
            font-weight: 500;
            color: #606266;
            margin-right: 10px;
            min-width: 100px;
          }

          .money-text {
            font-weight: 600;

            &.commission {
              color: #E6A23C;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .coupon-detail {
    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
    }

    .info-grid {
      grid-template-columns: 1fr !important;

      .info-item {
        flex-direction: column;
        align-items: flex-start;

        label {
          margin-bottom: 5px;
          min-width: auto;
        }
      }
    }

    .product-main-info,
    .bundle-main-info {
      flex-direction: column !important;

      .product-image,
      .bundle-image {
        align-self: center;
      }
    }

    .distribution-info {
      grid-template-columns: 1fr !important;

      .info-item {
        flex-direction: column;
        align-items: flex-start;

        label {
          margin-bottom: 5px;
          min-width: auto;
        }
      }
    }
  }
}
</style>
