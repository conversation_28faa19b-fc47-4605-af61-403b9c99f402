package com.tourism.miniprogram.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tourism.miniprogram.entity.CouponUsageRecord;

import java.util.List;

/**
 * 卡券使用记录服务接口
 *
 * <AUTHOR> Team
 * @since 2025-06-19
 */
public interface CouponUsageRecordService extends IService<CouponUsageRecord> {

    /**
     * 根据卡券ID获取使用记录列表
     *
     * @param couponId 卡券ID
     * @return 使用记录列表
     */
    List<CouponUsageRecord> getRecordsByCouponId(Integer couponId);

    /**
     * 根据卡券编码获取使用记录列表
     *
     * @param couponCode 卡券编码
     * @return 使用记录列表
     */
    List<CouponUsageRecord> getRecordsByCouponCode(String couponCode);

    /**
     * 根据用户ID获取使用记录列表
     *
     * @param userId 用户ID
     * @return 使用记录列表
     */
    List<CouponUsageRecord> getRecordsByUserId(Integer userId);

    /**
     * 根据景区ID获取使用记录列表
     *
     * @param scenicId 景区ID
     * @return 使用记录列表
     */
    List<CouponUsageRecord> getRecordsByScenicId(String scenicId);

    /**
     * 根据卡券ID和景区ID获取使用记录
     *
     * @param couponId 卡券ID
     * @param scenicId 景区ID
     * @return 使用记录
     */
    CouponUsageRecord getRecordByCouponAndScenic(Integer couponId, String scenicId);

    /**
     * 根据用户ID和景区ID获取使用记录列表
     *
     * @param userId 用户ID
     * @param scenicId 景区ID
     * @return 使用记录列表
     */
    List<CouponUsageRecord> getRecordsByUserAndScenic(Integer userId, String scenicId);

    /**
     * 根据使用状态获取记录列表
     *
     * @param usageStatus 使用状态
     * @return 使用记录列表
     */
    List<CouponUsageRecord> getRecordsByStatus(String usageStatus);

    /**
     * 创建组合包卡券的使用记录
     *
     * @param couponId 卡券ID
     * @param userId 用户ID
     * @param scenicIds 景区ID列表
     * @return 是否成功
     */
    boolean createBundleUsageRecords(Integer couponId, Integer userId, List<String> scenicIds);

    /**
     * 激活组合包景区
     * 为组合包卡券创建所有景区的使用记录，并激活当前景区
     *
     * @param couponId 卡券ID
     * @param userId 用户ID
     * @param currentScenicId 当前景区ID
     * @param allScenicIds 所有景区ID列表
     * @return 是否成功
     */
    boolean activateBundleScenic(Integer couponId, Integer userId, String currentScenicId, List<String> allScenicIds);

    /**
     * 标记卡券在指定景区已使用
     *
     * @param couponId 卡券ID
     * @param scenicId 景区ID
     * @return 是否成功
     */
    boolean markAsUsed(Integer couponId, String scenicId);

    /**
     * 批量创建使用记录
     *
     * @param records 使用记录列表
     * @return 是否成功
     */
    boolean batchCreateRecords(List<CouponUsageRecord> records);

    /**
     * 获取卡券在各景区的使用统计
     *
     * @param couponId 卡券ID
     * @return 使用统计列表
     */
    List<CouponUsageRecord> getUsageStatsByCouponId(Integer couponId);

    /**
     * 检查卡券在指定景区是否已使用
     *
     * @param couponId 卡券ID
     * @param scenicId 景区ID
     * @return 是否已使用
     */
    boolean isUsedInScenic(Integer couponId, String scenicId);

    /**
     * 获取卡券在指定景区的激活状态
     *
     * @param couponId 卡券ID
     * @param scenicId 景区ID
     * @return 激活状态（unactivated/active/used/expired/not_found）
     */
    String getScenicActivationStatus(Integer couponId, String scenicId);

    /**
     * 获取卡券的未使用景区列表
     *
     * @param couponId 卡券ID
     * @return 未使用的景区ID列表
     */
    List<String> getUnusedScenicIds(Integer couponId);

    /**
     * 获取卡券的已使用景区列表
     *
     * @param couponId 卡券ID
     * @return 已使用的景区ID列表
     */
    List<String> getUsedScenicIds(Integer couponId);

    /**
     * 处理过期的卡券使用记录，将满足条件的记录状态更新为'used'
     * 条件：valid_to不为null且valid_to小于当前时间（已过期）且usage_status不等于'used'（避免重复更新）
     *
     * @return 处理的记录数量
     */
    int processExpiredUsageRecords();
}
