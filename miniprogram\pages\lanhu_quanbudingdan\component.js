const apiService = require('../../utils/apiService');
const userService = require('../../utils/userService');

Page({
  data: {
    userId: null,
    orders: [],
    allOrders: [], // 存储所有订单数据用于筛选
    loading: false,
    activeTab: 'all', // all, pending, paid, review, canceled
    selectedOrderId: null, // 从参数传入的订单ID
    tabs: [
      { key: 'all', name: '全部', status: null },
      { key: 'pending', name: '待付款', status: 'pending' },
      { key: 'paid', name: '已付款', status: 'paid' },
      { key: 'review', name: '待评价', status: 'paid' },
      { key: 'canceled', name: '已取消', status: 'canceled' }
    ]
  },
  // 页面生命周期
  onLoad: function (options) {
    console.info("订单列表页面加载");
    // 接收页面参数
    if (options.orderId) {
      this.setData({
        selectedOrderId: options.orderId
      });
    }
    if (options.status) {
      this.setData({
        activeTab: options.status
      });
    }
    this.initPage();
  },

  onShow: function () {
    // 页面显示时刷新数据
    if (this.data.userId) {
      this.loadOrders(true);
    }
  },

  onUnload: function () {
    console.info("订单列表页面卸载");
  },

  // 下拉刷新
  onPullDownRefresh: function () {
    this.loadOrders(true).finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 移除上拉加载更多逻辑，因为直接加载全部数据
    // 初始化页面
    async initPage() {
      try {
        // 获取当前用户信息
        let userInfo;
        try {
          // 先尝试从本地存储获取用户信息
          const storedUserInfo = wx.getStorageSync('userInfo');
          if (storedUserInfo && storedUserInfo.id) {
            userInfo = storedUserInfo;
          } else {
            // 如果没有本地用户信息，使用默认用户ID
            userInfo = { id: 1, name: '用户' };
          }
        } catch (error) {
          console.error('获取用户信息失败:', error);
          // 使用默认用户ID
          userInfo = { id: 1, name: '用户' };
        }

        if (!userInfo || !userInfo.id) {
          wx.showToast({
            title: '请先登录',
            icon: 'none'
          });
          return;
        }

        this.setData({
          userId: userInfo.id
        });

        // 加载订单列表
        await this.loadOrders(true);
      } catch (error) {
        console.error('初始化页面失败:', error);
        wx.showToast({
          title: '页面加载失败',
          icon: 'none'
        });
      }
    },

    // 加载订单列表 - 直接加载全部数据
    async loadOrders(reset = false) {
      if (this.data.loading) return;

      try {
        this.setData({ loading: true });

        if (reset) {
          this.setData({
            orders: []
          });
        }

        // 获取用户的所有订单，使用大的分页参数来获取全部数据
        console.log('开始获取用户订单，用户ID:', this.data.userId);
        const response = await apiService.getUserOrders(this.data.userId, {
          current: 1,
          size: 1000 // 使用大的size来获取所有数据
        });

        console.log('订单API响应:', response);
        if (response && response.records && response.records.length > 0) {
          // 按创建时间倒序排列并格式化数据
          const sortedOrders = response.records.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

          // 预处理数据，添加格式化的字段和景区信息
          const processedOrders = await Promise.all(sortedOrders.map(async (order) => {
            const baseOrder = {
              ...order,
              formattedDate: this.formatDate(order.createdAt),
              statusText: this.getStatusText(order.status),
              scenicInfo: null // 初始化景区信息
            };

            // 获取订单的产品列表和景区信息
            try {
              await this.loadOrderScenicInfo(baseOrder);
            } catch (error) {
              console.error(`获取订单${order.id}的景区信息失败:`, error);
            }

            return baseOrder;
          }));

          // 存储所有订单数据
          this.setData({
            allOrders: processedOrders
          });

          // 根据当前选中的tab过滤订单
          this.filterOrders();
        } else {
          console.log('没有获取到订单数据或数据为空');
          this.setData({
            allOrders: [],
            orders: []
          });
        }
      } catch (error) {
        console.error('加载订单列表失败:', error);
        wx.showToast({
          title: '加载失败',
          icon: 'none'
        });
      } finally {
        this.setData({ loading: false });
      }
    },

    // 根据当前tab过滤订单
    filterOrders() {
      const { allOrders, activeTab } = this.data;
      let filteredOrders = allOrders;

      const currentTab = this.data.tabs.find(tab => tab.key === activeTab);
      if (currentTab && currentTab.status) {
        filteredOrders = allOrders.filter(order => order.status === currentTab.status);
      }

      this.setData({
        orders: filteredOrders
      });
    },

    // 切换标签
    onTabChange(e) {
      const tabKey = e.currentTarget.dataset.tab;
      if (tabKey === this.data.activeTab) return;

      this.setData({
        activeTab: tabKey
      });

      // 直接过滤订单，不重新加载
      this.filterOrders();
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`;
    },

    // 获取订单状态文本
    getStatusText(status) {
      const statusMap = {
        'pending': '待付款',
        'paid': '已付款',
        'canceled': '已取消'
      };
      return statusMap[status] || status;
    },

    // 取消订单
    async cancelOrder(e) {
      const orderId = e.currentTarget.dataset.orderId;

      try {
        const result = await wx.showModal({
          title: '确认取消',
          content: '确定要取消这个订单吗？'
        });

        if (result.confirm) {
          await apiService.updateOrderStatus(orderId, 'canceled');
          wx.showToast({
            title: '订单已取消',
            icon: 'success'
          });

          // 重新加载订单列表
          this.loadOrders(true);
        }
      } catch (error) {
        console.error('取消订单失败:', error);
        wx.showToast({
          title: '取消失败',
          icon: 'none'
        });
      }
    },

    // 立即支付
    payOrder(e) {
      const orderId = e.currentTarget.dataset.orderId;
      // 跳转到支付页面
      wx.navigateTo({
        url: `/pages/lanhu_querendingdan/component?orderId=${orderId}&from=orderList`
      });
    },

    // 再次购买
    buyAgain(e) {
      const orderId = e.currentTarget.dataset.orderId;
      const order = this.data.orders.find(o => o.id === parseInt(orderId));
      if (order) {
        // 根据订单信息跳转到产品详情页或首页
        wx.switchTab({
          url: '/pages/lanhu_shouye/component'
        });
      }
    },

    // 立即评价
    reviewOrder(e) {
      const orderId = e.currentTarget.dataset.orderId;
      // 跳转到评价页面（如果有的话）
      wx.showModal({
        title: '评价功能',
        content: '评价功能正在开发中，敬请期待！',
        showCancel: false
      });
    },

    // 获取订单的景区信息
    async loadOrderScenicInfo(order) {
      try {
        // 根据订单ID获取产品列表
        const products = await apiService.getProductsByOrderId(order.id);

        if (products && products.length > 0) {
          // 获取第一个产品
          const firstProduct = products[0];
          if (firstProduct.scenicId) {
            let scenicIdToQuery = firstProduct.scenicId;
            let isBundle = false;

            // 检查是否为组合包产品
            if (firstProduct.productType === 'bundle') {
              isBundle = true;
              // 如果是组合包，scenic_id 包含多个景区ID（用逗号分隔），只取第一个
              const scenicIds = firstProduct.scenicId.split(',');
              scenicIdToQuery = scenicIds[0].trim();
              console.log(`组合包产品，所有景区ID: ${firstProduct.scenicId}，使用第一个景区ID: ${scenicIdToQuery}`);
            }

            // 根据景区ID获取景区详情
            const scenicDetail = await apiService.getScenicDetail(scenicIdToQuery);

            if (scenicDetail) {
              // 构建标题，组合包产品需要添加标识
              let title = scenicDetail.title || '旅游讲解服务';
              if (isBundle) {
                title += '（组合包）';
              }

              // 将景区信息添加到订单中
              order.scenicInfo = {
                title: title,
                image: scenicDetail.image || '../../images/lanhu_quanbudingdan/FigmaDDSSlicePNG675e7299676300162f1e556d0ba5d74e.png',
                scenicId: scenicDetail.scenicId,
                address: scenicDetail.address,
                description: scenicDetail.description,
                isBundle: isBundle // 添加标识字段
              };

              console.log(`订单${order.id}的景区信息加载成功:`, order.scenicInfo);
            }
          }
        }
      } catch (error) {
        console.error(`加载订单${order.id}的景区信息失败:`, error);
        // 设置默认景区信息
        order.scenicInfo = {
          title: '旅游讲解服务',
          image: '../../images/lanhu_quanbudingdan/FigmaDDSSlicePNG675e7299676300162f1e556d0ba5d74e.png'
        };
      }
    }

});
