/**
 * 分销员API服务
 */
const httpService = require('./httpService');

class DistributorService {
  
  /**
   * 根据用户ID获取分销员信息
   * @param {number} userId 用户ID
   * @returns {Promise} 分销员信息
   */
  async getDistributorByUserId(userId) {
    try {
      console.log('获取分销员信息，用户ID:', userId);
      
      const result = await httpService.get(`/api/distributor/user/${userId}`, {}, {
        showLoading: false
      });
      
      console.log('分销员信息获取成功:', result);
      return result;
    } catch (error) {
      console.error('获取分销员信息失败:', error);
      // 如果是404错误，说明用户不是分销员，返回null而不是抛出错误
      if (error.statusCode === 404 || (error.data && error.data.code === 404)) {
        return null;
      }
      throw error;
    }
  }

  /**
   * 根据分销员编号获取分销员信息
   * @param {string} distributorCode 分销员编号
   * @returns {Promise} 分销员信息
   */
  async getDistributorByCode(distributorCode) {
    try {
      console.log('根据编号获取分销员信息:', distributorCode);
      
      const result = await httpService.get(`/api/distributor/code/${distributorCode}`, {}, {
        showLoading: false
      });
      
      console.log('分销员信息获取成功:', result);
      return result;
    } catch (error) {
      console.error('根据编号获取分销员信息失败:', error);
      throw error;
    }
  }

  /**
   * 获取分销员详情
   * @param {number} distributorId 分销员ID
   * @returns {Promise} 分销员详情
   */
  async getDistributorDetail(distributorId) {
    try {
      console.log('获取分销员详情，ID:', distributorId);
      
      const result = await httpService.get(`/api/distributor/${distributorId}`, {}, {
        showLoading: false
      });
      
      console.log('分销员详情获取成功:', result);
      return result;
    } catch (error) {
      console.error('获取分销员详情失败:', error);
      throw error;
    }
  }

  /**
   * 创建分销员
   * @param {number} userId 用户ID
   * @param {number} commissionRate 佣金比例（可选）
   * @returns {Promise} 创建结果
   */
  async createDistributor(userId, commissionRate = null) {
    try {
      console.log('创建分销员，用户ID:', userId, '佣金比例:', commissionRate);
      
      const params = { userId };
      if (commissionRate !== null) {
        params.commissionRate = commissionRate;
      }
      
      const result = await httpService.post('/api/distributor/create', params, {
        showLoading: true
      });
      
      console.log('分销员创建成功:', result);
      return result;
    } catch (error) {
      console.error('创建分销员失败:', error);
      throw error;
    }
  }

  /**
   * 生成分销员专属二维码
   * @param {number} distributorId 分销员ID
   * @returns {Promise} 二维码URL
   */
  async generateQrCode(distributorId) {
    try {
      console.log('生成分销员二维码，ID:', distributorId);
      
      const result = await httpService.post(`/api/distributor/${distributorId}/qrcode`, {}, {
        showLoading: true
      });
      
      console.log('二维码生成成功:', result);
      return result;
    } catch (error) {
      console.error('生成二维码失败:', error);
      throw error;
    }
  }

  /**
   * 获取分销员统计信息
   * @returns {Promise} 统计信息
   */
  async getDistributorStatistics() {
    try {
      console.log('获取分销员统计信息');
      
      const result = await httpService.get('/api/distributor/statistics', {}, {
        showLoading: false
      });
      
      console.log('分销员统计信息获取成功:', result);
      return result;
    } catch (error) {
      console.error('获取分销员统计信息失败:', error);
      throw error;
    }
  }

  /**
   * 检查用户是否为分销员
   * @param {number} userId 用户ID
   * @returns {Promise<boolean>} 是否为分销员
   */
  async checkIsDistributor(userId) {
    try {
      const distributor = await this.getDistributorByUserId(userId);
      return distributor !== null;
    } catch (error) {
      console.error('检查分销员身份失败:', error);
      return false;
    }
  }

  /**
   * 格式化金额显示
   * @param {number} amount 金额
   * @returns {string} 格式化后的金额
   */
  formatAmount(amount) {
    if (amount === null || amount === undefined) {
      return '0.00';
    }
    return Number(amount).toFixed(2);
  }

  /**
   * 格式化佣金比例显示
   * @param {number} rate 比例（百分比）
   * @returns {string} 格式化后的比例
   */
  formatCommissionRate(rate) {
    if (rate === null || rate === undefined) {
      return '0%';
    }
    return `${Number(rate).toFixed(1)}%`;
  }

  /**
   * 格式化状态显示
   * @param {string} status 状态
   * @returns {string} 中文状态
   */
  formatStatus(status) {
    const statusMap = {
      'active': '活跃',
      'inactive': '非活跃',
      'frozen': '冻结'
    };
    return statusMap[status] || status;
  }

  /**
   * 格式化日期显示
   * @param {string} dateString 日期字符串
   * @returns {string} 格式化后的日期
   */
  formatDate(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  /**
   * 格式化日期时间显示
   * @param {string} dateTimeString 日期时间字符串
   * @returns {string} 格式化后的日期时间
   */
  formatDateTime(dateTimeString) {
    if (!dateTimeString) return '';
    const date = new Date(dateTimeString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  }
}

// 创建单例实例
const distributorService = new DistributorService();

module.exports = distributorService;
